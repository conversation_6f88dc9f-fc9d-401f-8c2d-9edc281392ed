from PikaObj import *

def __init__(): ...

class EVENT:
    ALL: int
    PRESSED: int
    PRESSING: int
    PRESS_LOST: int
    SHORT_CLICKED: int
    LONG_PRESSED: int
    LONG_PRESSED_REPEAT: int
    CLICKED: int
    RELEASED: int
    SCROLL_BEGIN: int
    SCROLL_END: int
    SCROLL: int
    GESTURE: int
    KEY: int
    FOCUSED: int
    DEFOCUSED: int
    LEAVE: int
    HIT_TEST: int
    COVER_CHECK: int
    REFR_EXT_DRAW_SIZE: int
    DRAW_MAIN_BEGIN: int
    DRAW_MAIN: int
    DRAW_MAIN_END: int
    DRAW_POST_BEGIN: int
    DRAW_POST: int
    DRAW_POST_END: int
    DRAW_PART_BEGIN: int
    DRAW_PART_END: int
    VALUE_CHANGED: int
    INSERT: int
    REFRESH: int
    READY: int
    CANCEL: int
    DELETE: int
    CHILD_CHANGED: int
    CHILD_CREATED: int
    CHILD_DELETED: int
    SCREEN_UNLOAD_START: int
    SCREEN_LOAD_START: int
    SCREEN_LOADED: int
    SCREEN_UNLOADED: int
    SIZE_CHANGED: int
    STYLE_CHANGED: int
    LAYOUT_CHANGED: int
    GET_SELF_SIZE: int
    PREPROCESS: int
    def __init__(self): ...

class ALIGN:
    DEFAULT: int
    TOP_LEFT: int
    TOP_MID: int
    TOP_RIGHT: int
    BOTTOM_LEFT: int
    BOTTOM_MID: int
    BOTTOM_RIGHT: int
    LEFT_MID: int
    RIGHT_MID: int
    CENTER: int
    OUT_TOP_LEFT: int
    OUT_TOP_MID: int
    OUT_TOP_RIGHT: int
    OUT_BOTTOM_LEFT: int
    OUT_BOTTOM_MID: int
    OUT_BOTTOM_RIGHT: int
    OUT_LEFT_TOP: int
    OUT_LEFT_MID: int
    OUT_LEFT_BOTTOM: int
    OUT_RIGHT_TOP: int
    OUT_RIGHT_MID: int
    OUT_RIGHT_BOTTOM: int
    def __init__(self): ...

class PALETTE:
    RED: int
    PINK: int
    PURPLE: int
    DEEP_PURPLE: int
    INDIGO: int
    BLUE: int
    LIGHT_BLUE: int
    CYAN: int
    TEAL: int
    GREEN: int
    LIGHT_GREEN: int
    LIME: int
    YELLOW: int
    AMBER: int
    ORANGE: int
    DEEP_ORANGE: int
    BROWN: int
    BLUE_GREY: int
    GREY: int
    NONE: int
    def __init__(self): ...

class OPA:
    TRANSP: int
    COVER: int
    def __init__(self): ...

class ANIM:
    OFF: int
    ON: int
    def __init__(self): ...

class STATE:
    def __init__(self): ...

class lv_event:
    def get_code(self) -> int: ...
    def get_target(self) -> lv_obj: ...

class lv_color_t: ...

class lv_timer_t:
    def set_period(period: int): ...
    def set_cb(cb: any): ...
    def _del(self): ...

def palette_lighten(p: int, lvl: int) -> lv_color_t: ...
def palette_main(p: int) -> lv_color_t: ...

class style_t:
    def __init__(self): ...
    def init(self): ...
    def set_radius(self, radius: int): ...
    def set_bg_opa(self, opa: int): ...
    def set_bg_color(self, color: lv_color_t): ...
    def set_outline_width(self, w: int): ...
    def set_outline_color(self, color: lv_color_t): ...
    def set_outline_pad(self, pad: int): ...
    def set_shadow_width(self, w: int): ...
    def set_shadow_spread(self, s: int): ...
    def set_shadow_color(self, color: lv_color_t): ...

class lv_obj:
    def center(self): ...
    def set_size(self, size_x: int, size_y: int): ...
    def align(self, align: int, x_ofs: int, y_ofs: int): ...
    def set_height(self, h: int): ...
    def update_layout(self): ...
    def set_width(self, w: int): ...
    def add_state(self, state: int): ...
    def add_event(self, event_cb: any, filter: int, user_data: pointer): ...
    def add_style(self, style: style_t, selector: int): ...
    def get_x(self) -> int: ...
    def get_y(self) -> int: ...
    def set_pos(self, x: int, y: int): ...

class indev_t:
    def get_vect(self, point: point_t): ...

def obj(parent: lv_obj) -> lv_obj: ...
def indev_get_act() -> indev_t: ...

class point_t:
    def __init__(self): ...

class arc(lv_obj):
    MODE_NORMAL: int
    MODE_SYMMETRICAL: int
    MODE_REVERSE: int
    def __init__(self, parent: lv_obj): ...
    def set_start_angle(self, start: int): ...
    def set_end_angle(self, angle: int): ...
    def set_angles(self, start: int, end: int): ...
    def set_bg_start_angle(self, start: int): ...
    def set_bg_end_angle(self, angle: int): ...
    def set_bg_angles(self, start: int, end: int): ...
    def set_rotation(self, rotation: int): ...
    def set_mode(self, mode: int): ...
    def set_value(self, value: int): ...
    def set_range(self, min: int, max: int): ...
    def set_change_rate(self, rate: int): ...
    def get_angle_start(self) -> int: ...
    def get_angle_end(self) -> int: ...
    def get_bg_angle_start(self) -> int: ...
    def get_bg_angle_end(self) -> int: ...
    def get_value(self) -> int: ...
    def get_min_value(self) -> int: ...
    def get_max_value(self) -> int: ...
    def get_mode(self) -> int: ...
    # def get_rotation(self) -> int: ...

class bar(lv_obj):
    def __init__(self, parent: lv_obj): ...
    def set_value(self, value: int, anim: int): ...
    def set_start_value(self, start_value: int, anim: int): ...
    def set_range(self, min: int, max: int): ...
    def set_mode(self, mode: int): ...
    def get_value(self) -> int: ...
    def get_start_value(self) -> int: ...
    def get_min_value(self) -> int: ...
    def get_max_value(self) -> int: ...
    def get_mode(self) -> int: ...

class btn(lv_obj):
    def __init__(self, parent: lv_obj): ...

class checkbox(lv_obj):
    def __init__(self, parent: lv_obj): ...
    def set_text(self, txt: str): ...
    def set_text_static(self, txt: str): ...
    def get_text(self) -> str: ...

class dropdown(lv_obj):
    def __init__(self, parent: lv_obj): ...
    def set_text(self, txt: str): ...
    def set_options(self, options: str): ...
    def add_option(self, option: str, pos:int): ...
    def clear_options(self): ...
    def set_selected(self, sel_opt: int): ...
    def set_dir(self, dir: int): ...
    def set_symbol(self, symbol: str): ...
    def set_selected_highlight(self, en: int): ...
    # def get_list(self) -> lv_obj: ...
    def get_text(self) -> str: ...
    def get_options(self) -> str: ...
    def get_selected(self) -> int: ...
    def get_option_cnt(self) -> int: ...
    def get_selected_str(self) -> str: ...
    def get_option_index(self, option: str) -> int: ...
    def get_symbol(self) -> str: ...
    def get_selected_highlight(self) -> int: ...
    def get_dir(self) -> int: ...
    def open(self): ...
    def close(self): ...
    def is_open(self) -> int: ...

class label(lv_obj):
    def __init__(self, parent: lv_obj): ...
    def set_text(self, txt: str): ...
    def set_long_mode(self, mode: int): ...
    def set_recolor(self, en: int): ...
    def set_style_text_align(self, value: int, selector: int): ...

class roller(lv_obj):
    def __init__(self, parent: lv_obj): ...
    def set_options(self, options: str, mode: int): ...
    def set_visible_row_count(self, row_cnt: int): ...

class slider(lv_obj):
    def __init__(self, parent: lv_obj): ...

class switch(lv_obj):
    def __init__(self, parent: lv_obj): ...

class table(lv_obj):
    def __init__(self, parent: lv_obj): ...
    def set_cell_value(self, row: int, col: int, txt: str): ...

class textarea(lv_obj):
    def __init__(self, parent: lv_obj): ...
    def set_one_line(en: int): ...

def scr_act() -> lv_obj: ...
def timer_create_basic() -> lv_timer_t: ...
