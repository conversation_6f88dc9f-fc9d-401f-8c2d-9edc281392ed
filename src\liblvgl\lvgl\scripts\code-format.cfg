--style=kr
--indent=spaces=4
--indent-classes
--indent-switches
--indent-cases
--indent-preproc-block
--indent-preproc-define
--indent-col1-comments
--pad-oper
--unpad-paren
--align-pointer=middle
--align-reference=middle
--convert-tabs
--max-code-length=120
--break-after-logical
--break-closing-braces
--attach-closing-while
--min-conditional-indent=0
--max-continuation-indent=120
--mode=c
--lineend=linux
--suffix=none
--preserve-date
--formatted
--ignore-exclude-errors
--ignore-exclude-errors-x
--exclude=assets
--exclude=test_assets
--exclude=test_fonts
--exclude=../src/lv_conf_internal.h
--exclude=../src/core/lv_obj_style_gen.c
--exclude=../src/core/lv_obj_style_gen.h
--exclude=../src/libs/gif/gifdec.c
--exclude=../src/libs/gif/gifdec.h
--exclude=../src/libs/lodepng/lodepng.c
--exclude=../src/libs/lodepng/lodepng.h
--exclude=../src/libs/qrcode/qrcodegen.c
--exclude=../src/libs/qrcode/qrcodegen.h
--exclude=../src/libs/tjpgd/tjpgd.c
--exclude=../src/libs/tjpgd/tjpgd.h
--exclude=../src/libs/tjpgd/tjpgdcnf.h
--exclude=../src/libs/thorvg
--exclude=../src/libs/lz4
--exclude=../src/others/vg_lite_tvg/vg_lite.h
--exclude=../tests/unity/unity.c
--exclude=../tests/unity/unity_internals.h
--exclude=../tests/unity/unity_support.c
--exclude=../tests/unity/unity_support.h
--exclude=../tests/test_images
--exclude=../tests/build_test_defheap
--exclude=../tests/build_test_sysheap
