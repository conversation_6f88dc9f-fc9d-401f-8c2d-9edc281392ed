#include "../../lvgl.h"

#if LV_BUILD_EXAMPLES

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_HAND
    #define LV_ATTRIBUTE_IMAGE_IMG_HAND
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_IMAGE_IMG_HAND uint8_t img_hand_map[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x80, 0x80, 0x04, 0x63, 0x63, 0x63, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4f, 0x4f, 0x4f, 0x47, 0x03, 0x03, 0x03, 0x57, 0x22, 0x22, 0x22, 0x57, 0x3c, 0x3c, 0x3c, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x5b, 0x5b, 0x5b, 0x0c, 0x38, 0x38, 0x38, 0x74, 0x19, 0x19, 0x19, 0xeb, 0x18, 0x18, 0x18, 0xf0, 0x3a, 0x3a, 0x3a, 0xbc, 0x3e, 0x3e, 0x3e, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x11, 0x11, 0x03, 0x1e, 0x1e, 0x1e, 0x8f, 0x16, 0x16, 0x16, 0xfb, 0x04, 0x04, 0x04, 0xff, 0x06, 0x06, 0x06, 0xff, 0x0b, 0x0b, 0x0b, 0xec, 0x20, 0x20, 0x20, 0x87, 0x4a, 0x4a, 0x4a, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x7b, 0x7b, 0x7b, 0x0f, 0x35, 0x35, 0x35, 0xcb, 0x03, 0x03, 0x03, 0xff, 0x06, 0x06, 0x06, 0xff, 0x04, 0x04, 0x04, 0xff, 0x03, 0x03, 0x03, 0xff, 0x1a, 0x1a, 0x1a, 0xc4, 0xb9, 0xb9, 0xb9, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x27, 0x27, 0x27, 0xa0, 0x02, 0x02, 0x02, 0xff, 0x02, 0x02, 0x02, 0xff, 0x01, 0x01, 0x01, 0xff, 0x02, 0x02, 0x02, 0xff, 0x02, 0x02, 0x02, 0xff, 0x00, 0x00, 0x00, 0xff, 0x29, 0x29, 0x29, 0xf8, 0x16, 0x16, 0x16, 0xa8, 0x65, 0x65, 0x65, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x0d, 0x0d, 0x0d, 0x4b, 0x0d, 0x0d, 0x0d, 0xff, 0x17, 0x17, 0x17, 0xdb, 0x62, 0x62, 0x62, 0x54, 0x5c, 0x5c, 0x5c, 0x50, 0x0f, 0x0f, 0x0f, 0xb8, 0x05, 0x05, 0x05, 0xff, 0x22, 0x22, 0x22, 0xff, 0x39, 0x39, 0x39, 0xff, 0x3b, 0x3b, 0x3b, 0xff, 0x2f, 0x2f, 0x2f, 0xff, 0x3d, 0x3d, 0x3d, 0xff, 0x3d, 0x3d, 0x3d, 0xff, 0x3e, 0x3e, 0x3e, 0xff, 0x3f, 0x3f, 0x3f, 0xff, 0x40, 0x40, 0x40, 0xfb, 0x43, 0x43, 0x43, 0xe8, 0x44, 0x44, 0x44, 0xe4, 0x45, 0x45, 0x45, 0xdc, 0x46, 0x46, 0x46, 0xd7, 0x46, 0x46, 0x46, 0xd0, 0x49, 0x49, 0x49, 0xb3, 0x49, 0x49, 0x49, 0xb0, 0x49, 0x49, 0x49, 0xaf, 0x49, 0x49, 0x49, 0xab, 0x49, 0x49, 0x49, 0xa7, 0x46, 0x46, 0x46, 0x87, 0x45, 0x45, 0x45, 0x84, 0x44, 0x44, 0x44, 0x80, 0x43, 0x43, 0x43, 0x7f, 0x42, 0x42, 0x42, 0x7b, 0x00, 0x00, 0x00, 0x57, 0x00, 0x00, 0x00, 0x57, 0x00, 0x00, 0x00, 0x57, 0x01, 0x01, 0x01, 0x57, 0x02, 0x02, 0x02, 0x57, 0x00, 0x00, 0x00, 0x57, 0x00, 0x00, 0x00, 0x57, 0x02, 0x02, 0x02, 0x57, 0x04, 0x04, 0x04, 0x57, 0x06, 0x06, 0x06, 0x57, 0x04, 0x04, 0x04, 0x57, 0x04, 0x04, 0x04, 0x57, 0x05, 0x05, 0x05, 0x57, 0x07, 0x07, 0x07, 0x57, 0x08, 0x08, 0x08, 0x57, 0x06, 0x06, 0x06, 0x57, 0x07, 0x07, 0x07, 0x57, 0x08, 0x08, 0x08, 0x57, 0x0a, 0x0a, 0x0a, 0x57, 0x0c, 0x0c, 0x0c, 0x57, 0x0a, 0x0a, 0x0a, 0x57, 0x0b, 0x0b, 0x0b, 0x57, 0x0c, 0x0c, 0x0c, 0x57, 0x0f, 0x0f, 0x0f, 0x57, 0x11, 0x11, 0x11, 0x57, 0x10, 0x10, 0x10, 0x57, 0x10, 0x10, 0x10, 0x57, 0x12, 0x12, 0x12, 0x57, 0x14, 0x14, 0x14, 0x57, 0x16, 0x16, 0x16, 0x57, 0x13, 0x13, 0x13, 0x57, 0x14, 0x14, 0x14, 0x57, 0x15, 0x15, 0x15, 0x57, 0x30, 0x30, 0x30, 0x9c, 0x0c, 0x0c, 0x0c, 0xff, 0x06, 0x06, 0x06, 0xff, 0x0a, 0x0a, 0x0a, 0xff, 0x04, 0x04, 0x04, 0xff, 0x04, 0x04, 0x04, 0xff, 0x09, 0x09, 0x09, 0xff, 0x05, 0x05, 0x05, 0xff, 0x03, 0x03, 0x03, 0xff, 0x00, 0x00, 0x00, 0xff, 0x03, 0x03, 0x03, 0xff, 0x25, 0x25, 0x25, 0xff, 0x40, 0x40, 0x40, 0xa0, 0x18, 0x18, 0x18, 0x57, 0x37, 0x37, 0x37, 0x57, 0x3e, 0x3e, 0x3e, 0x57, 0x41, 0x41, 0x41, 0x57, 0x3e, 0x3e, 0x3e, 0x57, 0x35, 0x35, 0x35, 0x57, 0x34, 0x34, 0x34, 0x57, 0x33, 0x33, 0x33, 0x57, 0x35, 0x35, 0x35, 0x57, 0x35, 0x35, 0x35, 0x57, 0x2d, 0x2d, 0x2d, 0x57, 0x26, 0x26, 0x26, 0x57, 0x20, 0x20, 0x20, 0x57, 0x20, 0x20, 0x20, 0x57, 0x22, 0x22, 0x22, 0x57, 0x1f, 0x1f, 0x1f, 0x57, 0x19, 0x19, 0x19, 0x57, 0x14, 0x14, 0x14, 0x57, 0x15, 0x15, 0x15, 0x57, 0x18, 0x18, 0x18, 0x57, 0x14, 0x14, 0x14, 0x57, 0x16, 0x16, 0x16, 0x57, 0x44, 0x44, 0x44, 0x50,
    0x49, 0x49, 0x49, 0xb7, 0x04, 0x04, 0x04, 0xff, 0x35, 0x35, 0x35, 0x8c, 0x6f, 0x6f, 0x6f, 0x04, 0x6f, 0x6f, 0x6f, 0x0f, 0x21, 0x21, 0x21, 0x37, 0x0f, 0x0f, 0x0f, 0xff, 0x02, 0x02, 0x02, 0xff, 0x01, 0x01, 0x01, 0xff, 0x05, 0x05, 0x05, 0xff, 0x02, 0x02, 0x02, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x01, 0xff, 0x02, 0x02, 0x02, 0xff, 0x01, 0x01, 0x01, 0xff, 0x02, 0x02, 0x02, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x01, 0xff, 0x02, 0x02, 0x02, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x03, 0x03, 0x03, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x02, 0x02, 0x02, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x02, 0x02, 0x02, 0xff, 0x02, 0x02, 0x02, 0xff, 0x03, 0x03, 0x03, 0xff, 0x04, 0x04, 0x04, 0xff, 0x02, 0x02, 0x02, 0xff, 0x02, 0x02, 0x02, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x05, 0x05, 0x05, 0xff, 0x05, 0x05, 0x05, 0xff, 0x05, 0x05, 0x05, 0xff, 0x02, 0x02, 0x02, 0xff, 0x06, 0x06, 0x06, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x01, 0xff, 0x03, 0x03, 0x03, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x05, 0x05, 0x05, 0xff, 0x0c, 0x0c, 0x0c, 0xff, 0x10, 0x10, 0x10, 0xff, 0x16, 0x16, 0x16, 0xff, 0x15, 0x15, 0x15, 0xff, 0x14, 0x14, 0x14, 0xff, 0x16, 0x16, 0x16, 0xff, 0x18, 0x18, 0x18, 0xff, 0x18, 0x18, 0x18, 0xfc, 0x18, 0x18, 0x18, 0xfb, 0x17, 0x17, 0x17, 0xf8, 0x16, 0x16, 0x16, 0xf7, 0x16, 0x16, 0x16, 0xf4, 0x15, 0x15, 0x15, 0xf3, 0x14, 0x14, 0x14, 0xf3, 0x13, 0x13, 0x13, 0xf3, 0x10, 0x10, 0x10, 0xf0, 0x07, 0x07, 0x07, 0xec, 0x02, 0x02, 0x02, 0xeb, 0x03, 0x03, 0x03, 0xeb, 0x05, 0x05, 0x05, 0xeb, 0x02, 0x02, 0x02, 0xeb, 0x1b, 0x1b, 0x1b, 0xe8,
    0x38, 0x38, 0x38, 0x77, 0x0f, 0x0f, 0x0f, 0xff, 0x20, 0x20, 0x20, 0x8b, 0x80, 0x80, 0x80, 0x03, 0x6a, 0x6a, 0x6a, 0x14, 0x4e, 0x4e, 0x4e, 0x5c, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x04, 0x04, 0x04, 0xff, 0x03, 0x03, 0x03, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x02, 0x02, 0x02, 0xff, 0x06, 0x06, 0x06, 0xff, 0x06, 0x06, 0x06, 0xff, 0x05, 0x05, 0x05, 0xff, 0x06, 0x06, 0x06, 0xff, 0x09, 0x09, 0x09, 0xff, 0x0d, 0x0d, 0x0d, 0xff, 0x14, 0x14, 0x14, 0xff, 0x14, 0x14, 0x14, 0xff, 0x15, 0x15, 0x15, 0xff, 0x19, 0x19, 0x19, 0xff, 0x1c, 0x1c, 0x1c, 0xff, 0x26, 0x26, 0x26, 0xff, 0x26, 0x26, 0x26, 0xff, 0x26, 0x26, 0x26, 0xff, 0x26, 0x26, 0x26, 0xff, 0x25, 0x25, 0x25, 0xff, 0x37, 0x37, 0x37, 0xff, 0x3b, 0x3b, 0x3b, 0xff, 0x3e, 0x3e, 0x3e, 0xff, 0x3f, 0x3f, 0x3f, 0xfc, 0x41, 0x41, 0x41, 0xf7, 0x48, 0x48, 0x48, 0xb7, 0x49, 0x49, 0x49, 0xa8, 0x49, 0x49, 0x49, 0xa3, 0x49, 0x49, 0x49, 0xa3, 0x49, 0x49, 0x49, 0xa8, 0x48, 0x48, 0x48, 0x93, 0x43, 0x43, 0x43, 0x7f, 0x36, 0x36, 0x36, 0x6b, 0x22, 0x22, 0x22, 0x5c, 0x0a, 0x0a, 0x0a, 0x57, 0x01, 0x01, 0x01, 0x57, 0x03, 0x03, 0x03, 0x57, 0x09, 0x09, 0x09, 0x57, 0x0c, 0x0c, 0x0c, 0x57, 0x0c, 0x0c, 0x0c, 0x57, 0x0d, 0x0d, 0x0d, 0x57, 0x13, 0x13, 0x13, 0x57, 0x0f, 0x0f, 0x0f, 0x57, 0x1e, 0x1e, 0x1e, 0x9c, 0x07, 0x07, 0x07, 0xff, 0x00, 0x00, 0x00, 0xff, 0x05, 0x05, 0x05, 0xff, 0x03, 0x03, 0x03, 0xff, 0x01, 0x01, 0x01, 0xff, 0x07, 0x07, 0x07, 0xff, 0x05, 0x05, 0x05, 0xff, 0x01, 0x01, 0x01, 0xff, 0x02, 0x02, 0x02, 0xff, 0x00, 0x00, 0x00, 0xff, 0x07, 0x07, 0x07, 0xff, 0x31, 0x31, 0x31, 0xff, 0x45, 0x45, 0x45, 0x90, 0x12, 0x12, 0x12, 0x54, 0x25, 0x25, 0x25, 0x57, 0x36, 0x36, 0x36, 0x57, 0x3d, 0x3d, 0x3d, 0x57, 0x3f, 0x3f, 0x3f, 0x57, 0x47, 0x47, 0x47, 0x57, 0x50, 0x50, 0x50, 0x53, 0x55, 0x55, 0x55, 0x4b, 0x59, 0x59, 0x59, 0x44, 0x62, 0x62, 0x62, 0x37, 0x67, 0x67, 0x67, 0x2f, 0x6d, 0x6d, 0x6d, 0x27, 0x6e, 0x6e, 0x6e, 0x24, 0x6e, 0x6e, 0x6e, 0x24, 0x6e, 0x6e, 0x6e, 0x24, 0x79, 0x79, 0x79, 0x17, 0xa7, 0xa7, 0xa7, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x23, 0x23, 0x23, 0x30, 0x0b, 0x0b, 0x0b, 0xf4, 0x05, 0x05, 0x05, 0xff, 0x38, 0x38, 0x38, 0xff, 0x33, 0x33, 0x33, 0xff, 0x07, 0x07, 0x07, 0xff, 0x10, 0x10, 0x10, 0xff, 0x37, 0x37, 0x37, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2f, 0x2f, 0x2f, 0xcc, 0x02, 0x02, 0x02, 0xff, 0x01, 0x01, 0x01, 0xff, 0x03, 0x03, 0x03, 0xff, 0x03, 0x03, 0x03, 0xff, 0x01, 0x01, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x20, 0x20, 0x20, 0xf8, 0x14, 0x14, 0x14, 0xa8, 0x53, 0x53, 0x53, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x53, 0x53, 0x53, 0x2b, 0x0d, 0x0d, 0x0d, 0xf3, 0x0d, 0x0d, 0x0d, 0xff, 0x10, 0x10, 0x10, 0xff, 0x10, 0x10, 0x10, 0xf3, 0x17, 0x17, 0x17, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5a, 0x5a, 0x5a, 0x18, 0x34, 0x34, 0x34, 0xd8, 0x0d, 0x0d, 0x0d, 0xff, 0x02, 0x02, 0x02, 0xff, 0x05, 0x05, 0x05, 0xff, 0x0b, 0x0b, 0x0b, 0xec, 0x19, 0x19, 0x19, 0x87, 0x38, 0x38, 0x38, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7b, 0x7b, 0x7b, 0x0c, 0x3e, 0x3e, 0x3e, 0x5f, 0x41, 0x41, 0x41, 0x8c, 0x74, 0x74, 0x74, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0x39, 0x39, 0x54, 0x05, 0x05, 0x05, 0x57, 0x24, 0x24, 0x24, 0x57, 0x3c, 0x3c, 0x3c, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

const lv_image_dsc_t img_hand = {
    .header.w = 100,
    .header.h = 9,
    .header.stride = 400,
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .data = img_hand_map,
    .data_size = sizeof(img_hand_map),
};

#endif /* LV_BUILD_EXAMPLES */
