<?xml version="1.0" encoding="utf-8"?>

<!--
/****************************************************************************
*  Copyright 2024 Gorgon Meducer (Email:<EMAIL>)       *
*                                                                           *
*  Licensed under the Apache License, Version 2.0 (the "License");          *
*  you may not use this file except in compliance with the License.         *
*  You may obtain a copy of the License at                                  *
*                                                                           *
*     http://www.apache.org/licenses/LICENSE-2.0                            *
*                                                                           *
*  Unless required by applicable law or agreed to in writing, software      *
*  distributed under the License is distributed on an "AS IS" BASIS,        *
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. *
*  See the License for the specific language governing permissions and      *
*  limitations under the License.                                           *
*                                                                           *
****************************************************************************/
-->


<package schemaVersion="1.7.31" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="https://raw.githubusercontent.com/Open-CMSIS-Pack/Open-CMSIS-Pack-Spec/v1.7.31/schema/PACK.xsd">
  <vendor>LVGL</vendor>
  <name>lvgl</name>
  <description>LVGL (Light and Versatile Graphics Library) is a free and open-source graphics library providing everything you need to create an embedded GUI with easy-to-use graphical elements, beautiful visual effects and a low memory footprint.</description>
  <url>https://raw.githubusercontent.com/lvgl/lvgl/master/env_support/cmsis-pack/</url>
  <supportContact>https://github.com/lvgl/lvgl/issues/new/choose</supportContact>
  <license>LICENCE.txt</license>
  <!-- optional license file -->
  <!--
  <license>
  </license>
  -->

  <repository type="git">https://github.com/lvgl/lvgl.git</repository>

  <releases>
    <release date="2024-03-19" version="9.1.0" url="https://github.com/lvgl/lvgl/raw/v9.1.0/env_support/cmsis-pack/LVGL.lvgl.9.1.0.pack">
      - LVGL 9.1.0
      - See Change Log
    </release>
    <release date="2024-03-19" version="8.4.0" url="https://github.com/lvgl/lvgl/raw/v8.4.0/env_support/cmsis-pack/LVGL.lvgl.8.4.0.pack">
      - LVGL 8.4.0
      - Some minor fixes
    </release>
    <release date="2024-01-24" version="9.0.0" url="https://github.com/lvgl/lvgl/raw/v9.0.0/env_support/cmsis-pack/LVGL.lvgl.9.0.0.pack">
      - LVGL 9.0.0
      - Implements a New Render Architecture that enables parallel processing
      - Accelerates SW-Render with NEON and Helium technology for Cortex architectures. 
      - Adds supports for GPUs: VG-Lite, PXP, Dave2D and etc.
      - Adds display drivers
      - Adds Demos for benchmarks, render test etc,
      - Other fixes
    </release>
    
    <release date="2023-12-05" version="8.3.11" url="https://github.com/lvgl/lvgl/raw/8194d83226c27c84f12dd51e16f5add9939215a5/env_support/cmsis-pack/LVGL.lvgl.8.3.11.pack">
      - LVGL 8.3.11
      - Add LittleFS Library to LVGL8
      - Backport Tiny TTF to LVGL8
      - Some minor fixes
    </release>
    <release date="2023-09-19" version="8.3.10" url="https://github.com/lvgl/lvgl/raw/9e388055ec0bcad5179461e66d6dac6823129eee/env_support/cmsis-pack/LVGL.lvgl.8.3.10.pack">
      - LVGL 8.3.10
      - Some minor fixes
    </release>
    <release date="2023-08-04" version="8.3.9" url="https://github.com/lvgl/lvgl/raw/bdf5bfb88ce107f16cf9128cf75e61394b3219d0/env_support/cmsis-pack/LVGL.lvgl.8.3.9.pack">
      - LVGL 8.3.10
      - Add snapshot, fragment, imgfont, gridnav, msg and monkey
      - Other minor fixes
    </release>
    <release date="2023-07-04" version="8.3.8" url="https://github.com/lvgl/lvgl/raw/15433d69b9d8ae6aa74f49946874af81a0cc5921/env_support/cmsis-pack/LVGL.lvgl.8.3.8.pack">
      - LVGL 8.3.8
      - Add renesas-ra6m3 gpu adaptation
      - Improve performance and add more features for PXP and VGLite
      - Minor updates
    </release>
    <release date="2023-05-03" version="8.3.7" url="https://github.com/GorgonMeducer/lvgl/raw/a8195160fcb18b522b4a16c230455d48d99a0368/env_support/cmsis-pack/LVGL.lvgl.8.3.7.pack">
      - LVGL 8.3.7 release
      - Various fixes
    </release>
    <release date="2023-03-03" version="8.3.6" url="https://github.com/lvgl/lvgl/raw/6b0092c0d91b2c7bfded48e04cc7b486ed3a72bd/env_support/cmsis-pack/LVGL.lvgl.8.3.6.pack">
      - LVGL 8.3.6 release
      - Various fixes
    </release>
    <release date="2023-02-06" version="8.3.5" url="https://github.com/lvgl/lvgl/raw/6b0092c0d91b2c7bfded48e04cc7b486ed3a72bd/env_support/cmsis-pack/LVGL.lvgl.8.3.6.pack">
      - LVGL 8.3.5 release
      - Use LVGL version as the cmsis-pack version
      - Fix GPU support for NXP PXP and NXP VGLite
      - Rework stm32 DMA2D support
      - Various fixes
    </release>
    <release date="2022-12-31" version="1.0.12" url="https://github.com/lvgl/lvgl/raw/7d0de1aabeabd4c71231895df7a503a3313b4619/env_support/cmsis-pack/LVGL.lvgl.1.0.12.pack">
      - LVGL 9.0.0-dev
      - The final update for 2022, Happy New Year
    </release>
    <release date="2022-11-28" version="1.0.11" url="https://github.com/lvgl/lvgl/raw/f433ed62248cafd915848ff4d0720f97fb0fc7fd/env_support/cmsis-pack/LVGL.lvgl.1.0.11.pack">
      - LVGL 9.0.0-dev
      - Montyly update for November
    </release>
    <release date="2022-10-31" version="1.0.10" url="https://github.com/lvgl/lvgl/raw/0738d1ac36387c1ad5b6bf6f478bc315817bc34e/env_support/cmsis-pack/LVGL.lvgl.1.0.10.pack">
      - LVGL 9.0.0-dev
      - Montyly update for October
    </release>
    <release date="2022-09-16" version="1.0.9" url="https://github.com/lvgl/lvgl/raw/df7d5859f4f4886cb0320f2af1898c350e0ecd2a/env_support/cmsis-pack/LVGL.lvgl.1.0.9.pack">
      - LVGL 9.0.0-dev
      - Montyly update for September
      - Introduce a new component: File Explorer
      - Add support for TTF (Tiny TTF)
      - Add GPU Support for GD32F450-IPA
    </release>
    <release date="2022-08-30" version="1.0.8" url="https://github.com/lvgl/lvgl/raw/eb2e296d23b009aca7daf0e9be062d05b4b0048a/env_support/cmsis-pack/LVGL.lvgl.1.0.8.pack">
      - LVGL 9.0.0-dev
      - Add the binding for pikascript (an ultra-light-weight python VM)
      - Montyly update for August
    </release>
    <release date="2022-07-29" version="1.0.7" url="https://github.com/lvgl/lvgl/raw/b454a66e0be85976385c81cf9c9025f272a66f5d/env_support/cmsis-pack/LVGL.lvgl.1.0.7.pack">
      - LVGL 9.0.0-dev
      - Renderer refactory
      - Update GPU-Arm-2D
      - Other updates
    </release>
    <release date="2022-07-06" version="1.0.6" url="https://github.com/lvgl/lvgl/raw/49c59f4615857759cc8caf88424324ab6386c888/env_support/cmsis-pack/LVGL.lvgl.1.0.6.pack">
      - LVGL 8.3.0 release
      - Apply patch for memory leaking issue
      - Apply patch to speed up non normal blend mode
      - Add 9-key input mode to pinyin
      - Other minor changes
    </release>
    <release date="2022-06-29" version="1.0.5" url="https://github.com/GorgonMeducer/lvgl/raw/922108dbbe6d1c0be1069c342ca8693afee8c169/env_support/cmsis-pack/LVGL.lvgl.1.0.5.pack">
      - LVGL 8.3.0-dev
      - Monthly update for June
      - Add Pinyin as input method
      - Update benchmark to support RGB565-A8
      - Update support for layers
    </release>
    <release date="2022-05-31" version="1.0.4" url="https://github.com/lvgl/lvgl/raw/ce0605182c31e43abc8137ba21f237ec442735bc/env_support/cmsis-pack/LVGL.lvgl.1.0.4.pack">
      - LVGL 8.3.0-dev
      - Monthly update for May
      - Update drawing service
      - Update GPU support for Arm-2D library
      - Update GPU support for NXP PXP/VGLite
      - Improving the accuracy of benchmark.
      - Add new colour support for RGB565A8
    </release>
    <release date="2022-04-27" version="1.0.3" url="https://github.com/lvgl/lvgl/raw/b81437e96423826272cd42d5555373f15bfdf03a/env_support/cmsis-pack/LVGL.lvgl.1.0.3.pack">
      - LVGL 8.3.0-dev
      - Monthly update for April
      - Add GPU support for SWM341-DMA2D
    </release>
    <release date="2022-03-27" version="1.0.2" url="https://github.com/lvgl/lvgl/raw/a5b9a1c210821f122fb7582378a9f1819b1dc821/env_support/cmsis-pack/LVGL.lvgl.1.0.2.pack">
      - LVGL 8.3.0-dev
      - Monthly update for March
      - Add GPU support for Arm-2D library
    </release>
    <release date="2022-02-26" version="1.0.1" url="https://github.com/lvgl/lvgl/raw/44f6f752386617a8812228b9c1357f180e73e4ff/env_support/cmsis-pack/LVGL.lvgl.1.0.1.pack">
      - LVGL 8.3.0-dev
      - Monthly update for February
    </release>
    <release date="2022-01-31" version="1.0.0" url="https://github.com/lvgl/lvgl/blob/d851fe0528fcb920fee86c944fe9dbbaf6fbb0c9/env_support/cmsis-pack/LVGL.lvgl.1.0.0.pack?raw=true">
      - LVGL 8.2.0
      - Enable LV_TICK_CUSTOM when perf_counter is detected.
      - Celebrate Spring Festival
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Cortex-M</keyword>
    <keyword>SysTick</keyword>
    <keyword>Performance Analaysis</keyword>
  </keywords>

    <conditions>
        <!--
        <condition id="Arm Compiler">
            <description>Arm Compiler 5 (armcc) or Arm Compiler 6 (armclang).</description>
            <accept Tcompiler="ARMCC" Toptions="AC6"/>
            <accept Tcompiler="ARMCC" Toptions="AC6LTO"/>
            <accept Tcompiler="ARMCC" Toptions="AC5"/>
        </condition>
        <condition id="Arm GCC">
            <description>GNU Tools for Arm Embedded Processors.</description>
            <accept Tcompiler="GCC"/>
        </condition>
        <condition id="Cortex-M Processors">
            <description>Support All Cortex-M based processors</description>
            <accept  Dcore="Cortex-M0"/>
            <accept  Dcore="Cortex-M0+"/>
            <accept  Dcore="Cortex-M1"/>
            <accept  Dcore="Cortex-M3"/>
            <accept  Dcore="Cortex-M4"/>
            <accept  Dcore="Cortex-M7"/>
            <accept  Dcore="Cortex-M23"/>
            <accept  Dcore="Cortex-M33"/>
            <accept  Dcore="Cortex-M35P"/>
            <accept  Dcore="Cortex-M55"/>
            <accept  Dcore="SC000"/>
            <accept  Dcore="SC300"/>
            <accept  Dcore="ARMV8MBL"/>
            <accept  Dcore="ARMV8MML"/>
        </condition>

        <condition id="CMSIS-CORE">
            <description>Require CMSIS-CORE Support</description>
            <require Cclass="CMSIS" Cgroup="CORE"/>
        </condition>

        <condition id="Cortex-M Arm GCC">
            <description>Compile Cortex-M Processors with GNU Tools for Arm Embedded Processors.</description>
            <require condition="Arm GCC"/>
            <require condition="Cortex-M Processors"/>
        </condition>
        <condition id="Cortex-M Arm Compiler">
            <description>Compile Cortex-M Processors with GNU Tools for Arm Embedded Processors.</description>
            <require condition="Arm Compiler"/>
            <require condition="Cortex-M Processors"/>
        </condition>

        <condition id="Cortex-M Arm GCC CMSIS-CORE">
            <description>Compile Cortex-M Processors with GNU Tools for Arm Embedded Processors.</description>
            <require condition="Arm GCC"/>
            <require condition="Cortex-M Processors"/>
            <require condition="CMSIS-CORE"/>
        </condition>
        <condition id="Cortex-M Arm Compiler CMSIS-CORE">
            <description>Compile Cortex-M Processors with GNU Tools for Arm Embedded Processors.</description>
            <require condition="Arm Compiler"/>
            <require condition="Cortex-M Processors"/>
            <require condition="CMSIS-CORE"/>
        </condition>
        -->
        
        <condition id="GNU Assembler">
            <accept Tcompiler="ARMCC" Toptions="AC6"/>
            <accept Tcompiler="ARMCC" Toptions="AC6LTO"/>
            <accept Tcompiler="GCC"/>
            <accept Tcompiler="CLANG"/>
        </condition>
        
        <condition id="NEON">
            <description>Support NEON based processors</description>
            <accept  Dcore="Cortex-A8"/>
            <accept  Dcore="Cortex-A9"/>
            <accept  Dcore="Cortex-A15"/>
            <accept  Dcore="Cortex-A17"/>
            <!--
            <accept  Dcore="Cortex-A32"/>
            <accept  Dcore="Cortex-A35"/>
            -->
            <accept  Dcore="Cortex-A53"/>
            <accept  Dcore="Cortex-A57"/>
            <accept  Dcore="Cortex-A72"/>
            <accept  Dcore="Cortex-A73"/>
        </condition>
        
        <condition id="Helium">
            <description>Support Helium based processors</description>
            <accept  Dcore="Cortex-M52"/>
            <accept  Dcore="Cortex-M55"/>
            <accept  Dcore="Cortex-M85"/>
            <accept  Dcore="ARMV81MML"/>
        </condition>
        
        <condition id="NEON GNU Assembler">
            <require condition="NEON" />
            <require condition="GNU Assembler" />
        </condition>
        
        <condition id="Helium GNU Assembler">
            <require condition="Helium" />
            <require condition="GNU Assembler" />
        </condition>

        <condition id="LVGL-Essential">
            <description>Require LVGL Essential Service</description>
            <require Cclass="LVGL" Cgroup="Essential"/>
        </condition>

        <condition id="LVGL-Essential-Assets">
            <description>Require LVGL Essential Service and Demo Assets </description>
            <require condition="LVGL-Essential"/>
            <require Cclass="LVGL" Cgroup="Demos" Csub="Assets" />
        </condition>
        
        <condition id="Demo-Benchmark">
            <description>Dependency of Benchmark</description>
            <require condition="LVGL-Essential-Assets"/>
            <require Cclass="LVGL" Cgroup="Demos" Csub="Widgets" />
        </condition>

        <condition id="Arm-2D">
            <description>Require Arm-2D Support</description>
            <require Cclass="Acceleration" Cgroup="Arm-2D"/>
        </condition>

        <condition id="LVGL-GPU-STM32-DMA2D">
            <description>Condition for STM32-DMA2D</description>
            <require condition="LVGL-Essential"/>
            <!--<deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU STM32-DMA2D"/>-->
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU SWM341-DMA2D"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU NXP-PXP"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU NXP-VGLite"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU GD32-IPA"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU VG-Lite"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU Renesas-Dave2D"/>
        </condition>

        <condition id="LVGL-GPU-SWM341-DMA2D">
            <description>Condition for SWM341-DMA2D</description>
            <require condition="LVGL-Essential"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU STM32-DMA2D"/>
            <!--<deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU SWM341-DMA2D"/>-->
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU NXP-PXP"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU NXP-VGLite"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU VG-Lite"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU GD32-IPA"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU Renesas-Dave2D"/>
        </condition>

        <condition id="LVGL-GPU-NXP-PXP">
            <description>Condition for NXP-PXP</description>
            <require condition="LVGL-Essential"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU STM32-DMA2D"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU SWM341-DMA2D"/>
            <!--<deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU NXP-PXP"/>-->
            <!--<deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU NXP-VGLite"/>-->
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU VG-Lite"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU GD32-IPA"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU Renesas-Dave2D"/>
        </condition>

        <condition id="LVGL-GPU-NXP-VGLite">
            <description>Condition for NXP-VGLite</description>
            <require condition="LVGL-Essential"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU STM32-DMA2D"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU SWM341-DMA2D"/>
            <!--<deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU NXP-PXP"/>-->
            <!--<deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU NXP-VGLite"/>-->
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU VG-Lite"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU GD32-IPA"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU Renesas-Dave2D"/>
        </condition>

        <condition id="LVGL-GPU-GD32-IPA">
            <description>Condition for GD32-IPA</description>
            <require condition="LVGL-Essential"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU STM32-DMA2D"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU SWM341-DMA2D"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU NXP-PXP"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU NXP-VGLite"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU VG-Lite"/>
            <!--<deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU GD32-IPA"/>-->
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU Renesas-Dave2D"/>
        </condition>
        
        <condition id="LVGL-GPU-Renesas-Dave2D">
            <description>Condition for Renesas-Dave2D</description>
            <require condition="LVGL-Essential"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU STM32-DMA2D"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU SWM341-DMA2D"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU NXP-PXP"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU NXP-VGLite"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU VG-Lite"/>
            <deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU GD32-IPA"/>
            <!--<deny Cclass="LVGL" Cgroup="LVGL9" Csub="GPU Renesas-Dave2D"/>-->
        </condition>

    </conditions>
  <!-- apis section (optional - for Application Programming Interface descriptions) -->
  <!--
  <apis>
  </apis>
  -->

  <!-- boards section (mandatory for Board Support Packs) -->
  <!--
  <boards>
  </boards>
  -->

  <!-- devices section (mandatory for Device Family Packs) -->
  <!--
  <devices>
  </devices>
  -->

  <!-- examples section (optional for all Software Packs)-->
  <!--
  <examples>
  </examples>
  -->

  <!-- conditions section (optional for all Software Packs)-->
  <!--
  <conditions>
  </conditions>
  -->

    <components>
        <bundle Cbundle="LVGL9" Cclass="LVGL" Cversion="9.1.0">
            <description>LVGL (Light and Versatile Graphics Library) is a free and open-source graphics library providing everything you need to create an embedded GUI with easy-to-use graphical elements, beautiful visual effects and a low memory footprint.</description>
            <doc></doc>
            <component Cgroup="Essential">
              <description>The Essential services of LVGL (without extra content)</description>
              <files>
              
                <!-- src -->
                <file category="sourceC"            name="src/lv_init.c" />
                
                <!-- src/core -->
                <file category="header"             name="src/core/lv_global.h" />
                <file category="sourceC"            name="src/core/lv_group.c" />
                <file category="sourceC"            name="src/core/lv_obj.c" />
                <file category="sourceC"            name="src/core/lv_obj_class.c" />
                <file category="sourceC"            name="src/core/lv_obj_draw.c" />
                <file category="sourceC"            name="src/core/lv_obj_event.c" />
                <file category="sourceC"            name="src/core/lv_obj_id_builtin.c" />
                <file category="sourceC"            name="src/core/lv_obj_pos.c" />
                <file category="sourceC"            name="src/core/lv_obj_property.c" />
                <file category="sourceC"            name="src/core/lv_obj_scroll.c" />
                <file category="sourceC"            name="src/core/lv_obj_style.c" />
                <file category="sourceC"            name="src/core/lv_obj_style_gen.c" />
                <file category="sourceC"            name="src/core/lv_obj_tree.c" />
                <file category="sourceC"            name="src/core/lv_refr.c" />
                
                
                <!-- src/drivers -->
                <file category="sourceC"            name="src/drivers/evdev/lv_evdev.c" />
                
                <file category="sourceC"            name="src/drivers/display/lcd/lv_lcd_generic_mipi.c" />
                
                <file category="sourceC"            name="src/drivers/nuttx/lv_nuttx_entry.c" />
                <file category="sourceC"            name="src/drivers/nuttx/lv_nuttx_cache.c" />
                <file category="sourceC"            name="src/drivers/nuttx/lv_nuttx_fbdev.c" />
                <file category="sourceC"            name="src/drivers/nuttx/lv_nuttx_lcd.c" />
                <file category="sourceC"            name="src/drivers/nuttx/lv_nuttx_libuv.c" />
                <file category="sourceC"            name="src/drivers/nuttx/lv_nuttx_touchscreen.c" />
                <file category="sourceC"            name="src/drivers/nuttx/lv_nuttx_image_cache.c" />
                
                <file category="sourceC"            name="src/drivers/sdl/lv_sdl_keyboard.c" />
                <file category="sourceC"            name="src/drivers/sdl/lv_sdl_mouse.c" />
                <file category="sourceC"            name="src/drivers/sdl/lv_sdl_mousewheel.c" />
                <file category="sourceC"            name="src/drivers/sdl/lv_sdl_window.c" />
                
                <file category="sourceC"            name="src/drivers/x11/lv_x11_display.c" />
                <file category="sourceC"            name="src/drivers/x11/lv_x11_input.c" />
                
                <file category="sourceC"            name="src/drivers/libinput/lv_libinput.c" />
                <file category="sourceC"            name="src/drivers/libinput/lv_xkb.c" />

                <!-- src/drivers -->
                <file category="sourceC"            name="src/display/lv_display.c" />
                

                <!-- src/draw -->
                <file category="sourceC"            name="src/draw/lv_draw.c" />
                <file category="sourceC"            name="src/draw/lv_draw_arc.c" />
                <file category="sourceC"            name="src/draw/lv_draw_buf.c" />
                <file category="sourceC"            name="src/draw/lv_draw_image.c" />
                <file category="sourceC"            name="src/draw/lv_draw_label.c" />
                <file category="sourceC"            name="src/draw/lv_draw_line.c" />
                <file category="sourceC"            name="src/draw/lv_draw_mask.c" />
                <file category="sourceC"            name="src/draw/lv_draw_rect.c" />
                <file category="sourceC"            name="src/draw/lv_draw_triangle.c" />
                <file category="sourceC"            name="src/draw/lv_draw_vector.c" />
                <file category="sourceC"            name="src/draw/lv_image_decoder.c" />
                
                <!-- src/draw/sw -->
                <file category="sourceC"            name="src/draw/sw/lv_draw_sw.c" />
                <file category="sourceC"            name="src/draw/sw/lv_draw_sw_arc.c" />
                <file category="sourceC"            name="src/draw/sw/lv_draw_sw_border.c" />
                <file category="sourceC"            name="src/draw/sw/lv_draw_sw_box_shadow.c" />
                <file category="sourceC"            name="src/draw/sw/lv_draw_sw_fill.c" />
                <file category="sourceC"            name="src/draw/sw/lv_draw_sw_gradient.c" />
                <file category="sourceC"            name="src/draw/sw/lv_draw_sw_img.c" />
                <file category="sourceC"            name="src/draw/sw/lv_draw_sw_letter.c" />
                <file category="sourceC"            name="src/draw/sw/lv_draw_sw_line.c" />
                <file category="sourceC"            name="src/draw/sw/lv_draw_sw_mask.c" />
                <file category="sourceC"            name="src/draw/sw/lv_draw_sw_mask_rect.c" />
                <file category="sourceC"            name="src/draw/sw/lv_draw_sw_transform.c" />
                <file category="sourceC"            name="src/draw/sw/lv_draw_sw_triangle.c" />
                <file category="sourceC"            name="src/draw/sw/lv_draw_sw_vector.c" />
                
                <!-- src/draw/sw/blend -->
                <file category="sourceC"            name="src/draw/sw/blend/lv_draw_sw_blend.c" />
                <file category="sourceC"            name="src/draw/sw/blend/lv_draw_sw_blend_to_argb8888.c" />
                <file category="sourceC"            name="src/draw/sw/blend/lv_draw_sw_blend_to_rgb565.c" />
                <file category="sourceC"            name="src/draw/sw/blend/lv_draw_sw_blend_to_rgb888.c" />
                <file category="sourceAsm"          name="src/draw/sw/blend/neon/lv_blend_neon.S"  condition="NEON GNU Assembler"/>
                <file category="sourceAsm"          name="src/draw/sw/blend/helium/lv_blend_helium.S"  condition="Helium GNU Assembler"/>

                <!-- src/font -->
                <file category="sourceC"            name="src/font/lv_binfont_loader.c" />
                <file category="sourceC"            name="src/font/lv_font.c" />
                <file category="sourceC"            name="src/font/lv_font_dejavu_16_persian_hebrew.c" />
                <file category="sourceC"            name="src/font/lv_font_fmt_txt.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_8.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_10.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_12.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_14.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_16.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_18.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_20.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_22.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_24.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_26.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_28.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_28_compressed.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_30.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_32.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_34.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_36.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_38.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_40.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_42.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_44.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_46.c" />
                <file category="sourceC"            name="src/font/lv_font_montserrat_48.c" />
                <file category="sourceC"            name="src/font/lv_font_simsun_16_cjk.c" />
                <file category="sourceC"            name="src/font/lv_font_unscii_8.c" />
                <file category="sourceC"            name="src/font/lv_font_unscii_16.c" />


                <!-- src/indev -->
                <file category="sourceC"            name="src/indev/lv_indev.c" />
                <file category="sourceC"            name="src/indev/lv_indev_scroll.c" />
                
                <!-- src/layouts -->
                <file category="sourceC"            name="src/layouts/lv_layout.c" />
                <file category="sourceC"            name="src/layouts/flex/lv_flex.c" />
                <file category="sourceC"            name="src/layouts/grid/lv_grid.c" />


                <!-- src/misc-->
                <file category="sourceC"            name="src/misc/lv_anim.c" />
                <file category="sourceC"            name="src/misc/lv_anim_timeline.c" />
                <file category="sourceC"            name="src/misc/lv_area.c" />
                <file category="sourceC"            name="src/misc/lv_array.c" />
                <file category="sourceC"            name="src/misc/lv_async.c" />
                <file category="sourceC"            name="src/misc/lv_bidi.c" />
                <file category="sourceC"            name="src/misc/lv_color.c" />
                <file category="sourceC"            name="src/misc/lv_color_op.c" />
                <file category="sourceC"            name="src/misc/lv_event.c" />
                <file category="sourceC"            name="src/misc/lv_fs.c" />
                <file category="sourceC"            name="src/misc/lv_ll.c" />
                <file category="sourceC"            name="src/misc/lv_log.c" />
                <file category="sourceC"            name="src/misc/lv_lru.c" />
                <file category="sourceC"            name="src/misc/lv_math.c" />
                <file category="sourceC"            name="src/misc/lv_palette.c" />
                <file category="sourceC"            name="src/misc/lv_profiler_builtin.c" />
                <file category="sourceC"            name="src/misc/lv_rb.c" />
                <file category="sourceC"            name="src/misc/lv_style.c" />
                <file category="sourceC"            name="src/misc/lv_style_gen.c" />
                <file category="sourceC"            name="src/misc/lv_templ.c" />
                <file category="sourceC"            name="src/misc/lv_text.c" />
                <file category="sourceC"            name="src/misc/lv_text_ap.c" />
                <file category="sourceC"            name="src/misc/lv_timer.c" />
                <file category="sourceC"            name="src/misc/lv_utils.c" />
                
                <!-- src/misc/cache-->
                <file category="sourceC"            name="src/misc/cache/_lv_cache_lru_rb.c" />
                <file category="sourceC"            name="src/misc/cache/lv_cache.c" />
                <file category="sourceC"            name="src/misc/cache/lv_cache_entry.c" />
                <file category="sourceC"            name="src/misc/cache/lv_image_cache.c" />

                <!-- src/stdlib-->
                <file category="sourceC"            name="src/stdlib/lv_mem.c" />
                <file category="sourceC"            name="src/stdlib/builtin/lv_mem_core_builtin.c" />
                <file category="sourceC"            name="src/stdlib/builtin/lv_sprintf_builtin.c" />
                <file category="sourceC"            name="src/stdlib/builtin/lv_string_builtin.c" />
                <file category="sourceC"            name="src/stdlib/builtin/lv_tlsf.c" />
                <file category="sourceC"            name="src/stdlib/clib/lv_mem_core_clib.c" />
                <file category="sourceC"            name="src/stdlib/clib/lv_sprintf_clib.c" />
                <file category="sourceC"            name="src/stdlib/clib/lv_string_clib.c" />
                <file category="sourceC"            name="src/stdlib/micropython/lv_mem_core_micropython.c" />
                <file category="sourceC"            name="src/stdlib/rtthread/lv_mem_core_rtthread.c" />
                <file category="sourceC"            name="src/stdlib/rtthread/lv_sprintf_rtthread.c" />
                <file category="sourceC"            name="src/stdlib/rtthread/lv_string_rtthread.c" />
                
                
                <!-- src/themes -->
                <file category="sourceC"            name="src/themes/lv_theme.c" />
                <file category="sourceC"            name="src/themes/mono/lv_theme_mono.c" />
                <file category="sourceC"            name="src/themes/simple/lv_theme_simple.c" />
                <file category="sourceC"            name="src/themes/default/lv_theme_default.c" />
                
                <!-- src/tick -->
                <file category="sourceC"            name="src/tick/lv_tick.c" />
                
                <!-- src/widgets -->
                <file category="sourceC"            name="src/widgets/animimage/lv_animimage.c" />
                <file category="sourceC"            name="src/widgets/arc/lv_arc.c" />
                <file category="sourceC"            name="src/widgets/bar/lv_bar.c" />
                <file category="sourceC"            name="src/widgets/button/lv_button.c" />
                <file category="sourceC"            name="src/widgets/buttonmatrix/lv_buttonmatrix.c" />
                <file category="sourceC"            name="src/widgets/calendar/lv_calendar.c" />
                <file category="sourceC"            name="src/widgets/calendar/lv_calendar_header_arrow.c" />
                <file category="sourceC"            name="src/widgets/calendar/lv_calendar_header_dropdown.c" />
                <file category="sourceC"            name="src/widgets/canvas/lv_canvas.c" />
                <file category="sourceC"            name="src/widgets/chart/lv_chart.c" />
                <file category="sourceC"            name="src/widgets/checkbox/lv_checkbox.c" />
                <file category="sourceC"            name="src/widgets/dropdown/lv_dropdown.c" />
                <file category="sourceC"            name="src/widgets/image/lv_image.c" />
                <file category="sourceC"            name="src/widgets/imagebutton/lv_imagebutton.c" />
                <file category="sourceC"            name="src/widgets/keyboard/lv_keyboard.c" />
                <file category="sourceC"            name="src/widgets/label/lv_label.c" />
                <file category="sourceC"            name="src/widgets/led/lv_led.c" />
                <file category="sourceC"            name="src/widgets/line/lv_line.c" />
                <file category="sourceC"            name="src/widgets/list/lv_list.c" />
                <file category="sourceC"            name="src/widgets/menu/lv_menu.c" />
                <file category="sourceC"            name="src/widgets/msgbox/lv_msgbox.c" />
                <file category="sourceC"            name="src/widgets/objx_templ/lv_objx_templ.c" />
                <file category="sourceC"            name="src/widgets/roller/lv_roller.c" />
                <file category="sourceC"            name="src/widgets/scale/lv_scale.c" />
                <file category="sourceC"            name="src/widgets/slider/lv_slider.c" />
                <file category="sourceC"            name="src/widgets/span/lv_span.c" />
                <file category="sourceC"            name="src/widgets/spinbox/lv_spinbox.c" />
                <file category="sourceC"            name="src/widgets/spinner/lv_spinner.c" />
                <file category="sourceC"            name="src/widgets/switch/lv_switch.c" />
                <file category="sourceC"            name="src/widgets/table/lv_table.c" />
                <file category="sourceC"            name="src/widgets/tabview/lv_tabview.c" />
                <file category="sourceC"            name="src/widgets/textarea/lv_textarea.c" />
                <file category="sourceC"            name="src/widgets/tileview/lv_tileview.c" />
                <file category="sourceC"            name="src/widgets/win/lv_win.c" />

                <!-- src/libs -->
                <file category="sourceC"            name="src/libs/bin_decoder/lv_bin_decoder.c" />
                <file category="sourceC"            name="src/libs/fsdrv/lv_fs_cbfs.c" />
                
                <!-- src/others -->
                <file category="sourceC"            name="src/others/sysmon/lv_sysmon.c" />
                <file category="sourceC"            name="src/others/observer/lv_observer.c" />
                <file category="sourceC"            name="src/others/vg_lite_tvg/vg_lite_matrix.c" />
                <file category="sourceCpp"          name="src/others/vg_lite_tvg/vg_lite_tvg.cpp" />
                
                <!-- src/demons -->
                <file category="sourceC"            name="demos/lv_demos.c" />
                
                <!-- src/osal -->
                <file category="sourceC"            name="src/osal/lv_os_none.c"/>
                
                <!-- general -->
                <file category="preIncludeGlobal"   name="lv_conf_cmsis.h" attr="config" version="2.1.5" />
                <file category="sourceC"            name="lv_cmsis_pack.c" />
                <file category="header"             name="lvgl.h" />
                <file category="doc"                name="README.md"/>

                <!-- code template -->
                <file category="header"       name="examples/porting/lv_port_disp_template.h" attr="template" select="Display port template" version="2.1.0"/>
                <file category="sourceC"      name="examples/porting/lv_port_disp_template.c" attr="template" select="Display port template" version="2.1.0"/>
                <file category="header"       name="examples/porting/lv_port_indev_template.h" attr="template" select="Input devices port template" version="2.1.0"/>
                <file category="sourceC"      name="examples/porting/lv_port_indev_template.c" attr="template" select="Input devices port template" version="2.1.0"/>
                <file category="header"       name="examples/porting/lv_port_fs_template.h" attr="template" select="File system port template" version="2.1.0"/>
                <file category="sourceC"      name="examples/porting/lv_port_fs_template.c" attr="template" select="File system port template" version="2.1.0"/>
              </files>

              <Pre_Include_Global_h>

/*! \brief use lv_config_cmsis.h which will be pre-included */
#define LV_CONF_SKIP
#define LV_LVGL_H_INCLUDE_SIMPLE    1
              </Pre_Include_Global_h>

               <RTE_Components_h>

/*! \brief Enable LVGL */
#define RTE_GRAPHICS_LVGL
               </RTE_Components_h>

            </component>

            <component Cgroup="Display" Cvariant="Windows Backend" condition="LVGL-Essential">
              <description>Add the display driver for Windows backend  </description>
              <files>
                <file category="sourceC"            name="src/drivers/windows/lv_windows_context.c" />
                <file category="sourceC"            name="src/drivers/windows/lv_windows_display.c" />
                <file category="sourceC"            name="src/drivers/windows/lv_windows_input.c" />
              </files>

              <RTE_Components_h>
/* use display driver for Windows backend*/ */
#define LV_USE_WINDOWS    1

              </RTE_Components_h>
            </component>

            <component Cgroup="Display" Cvariant="Linux DRM" condition="LVGL-Essential">
              <description>Add the display driver for Linux /dev/dri/card*/  </description>
              <files>
                <file category="sourceC"            name="src/drivers/display/drm/lv_linux_drm.c" />
              </files>

              <RTE_Components_h>
/* use display driver for Linux /dev/dri/card*/ */
#define LV_USE_LINUX_DRM    1

              </RTE_Components_h>
            </component>

            <component Cgroup="Display" Cvariant="Linux FBDEV" condition="LVGL-Essential">
              <description>Add the display driver for Linux /dev/fb </description>
              <files>
                <file category="sourceC"            name="src/drivers/display/fb/lv_linux_fbdev.c" />
                <file category="sourceCpp"          name="src/drivers/display/tft_espi/lv_tft_espi.cpp" />
              </files>

              <RTE_Components_h>
/* use display driver for Linux /dev/fb */
#define LV_USE_LINUX_FBDEV    1

              </RTE_Components_h>
            </component>

            <component Cgroup="Display" Cvariant="TFT_eSPI" condition="LVGL-Essential">
              <description>Add the display driver for TFT_eSPI </description>
              <files>
                <file category="sourceCpp"          name="src/drivers/display/tft_espi/lv_tft_espi.cpp" />
              </files>

              <RTE_Components_h>
/* use display driver for TFT_eSPI */
#define LV_USE_TFT_ESPI    1

              </RTE_Components_h>
            </component>

            <component Cgroup="Display" Cvariant="ILI9341" condition="LVGL-Essential">
              <description>Add the display driver for ILI9341 </description>
              <files>
                <file category="sourceC"            name="src/drivers/display/ili9341/lv_ili9341.c" />
              </files>

              <RTE_Components_h>
/* use display driver for ILI9341 */
#define LV_USE_ILI9341	    1

              </RTE_Components_h>
            </component>

            <component Cgroup="Display" Cvariant="st7735" condition="LVGL-Essential">
              <description>Add the display driver for st7735 </description>
              <files>
                <file category="sourceC"            name="src/drivers/display/st7735/lv_st7735.c" />
              </files>

              <RTE_Components_h>
/* use display driver for st7735 */
#define LV_USE_ST7735	    1

              </RTE_Components_h>
            </component>

            <component Cgroup="Display" Cvariant="st7789" condition="LVGL-Essential">
              <description>Add the display driver for st7789 </description>
              <files>
                <file category="sourceC"            name="src/drivers/display/st7789/lv_st7789.c" />
              </files>

              <RTE_Components_h>
/* use display driver for st7789 */
#define LV_USE_ST7789	    1

              </RTE_Components_h>
            </component>

            <component Cgroup="Display" Cvariant="st7796" condition="LVGL-Essential">
              <description>Add the display driver for st7796 </description>
              <files>
                <file category="sourceC"            name="src/drivers/display/st7796/lv_st7796.c" />
              </files>

              <RTE_Components_h>
/* use display driver for st7796 */
#define LV_USE_ST7796	    1

              </RTE_Components_h>
            </component>

            <component Cgroup="OS Abstraction Layer" Cvariant="pthreads" condition="LVGL-Essential">
              <description>Add the support for pthreads</description>
              <files>
                <file category="sourceC"      name="src/osal/lv_pthread.c"/>
              </files>

              <RTE_Components_h>

/*   Select an operating system to use. Possible options:
 * - LV_OS_NONE
 * - LV_OS_PTHREAD
 * - LV_OS_FREERTOS
 * - LV_OS_CMSIS_RTOS2
 * - LV_OS_RTTHREAD
 * - LV_OS_WINDOWS
 * - LV_OS_CUSTOM
 */
#define LV_USE_OS   LV_OS_PTHREAD

              </RTE_Components_h>

            </component>

            <component Cgroup="OS Abstraction Layer" Cvariant="FreeRTOS" condition="LVGL-Essential">
              <description>Add the support for FreeRTOS</description>
              <files>
                <file category="sourceC"      name="src/osal/lv_freertos.c"/>
              </files>

              <RTE_Components_h>

/*   Select an operating system to use. Possible options:
 * - LV_OS_NONE
 * - LV_OS_PTHREAD
 * - LV_OS_FREERTOS
 * - LV_OS_CMSIS_RTOS2
 * - LV_OS_RTTHREAD
 * - LV_OS_WINDOWS
 * - LV_OS_CUSTOM
 */
#define LV_USE_OS   LV_OS_FREERTOS

              </RTE_Components_h>

            </component>

            <component Cgroup="OS Abstraction Layer" Cvariant="CMSIS-RTOS2" isDefaultVariant="true" condition="LVGL-Essential">
              <description>Add the support for CMSIS-RTOS2 APIs</description>
              <files>
                <file category="sourceC"      name="src/osal/lv_cmsis_rtos2.c"/>
              </files>

              <RTE_Components_h>

/*   Select an operating system to use. Possible options:
 * - LV_OS_NONE
 * - LV_OS_PTHREAD
 * - LV_OS_FREERTOS
 * - LV_OS_CMSIS_RTOS2
 * - LV_OS_RTTHREAD
 * - LV_OS_WINDOWS
 * - LV_OS_CUSTOM
 */
#define LV_USE_OS   LV_OS_CMSIS_RTOS2

              </RTE_Components_h>

            </component>

            <component Cgroup="OS Abstraction Layer" Cvariant="RT-Thread" condition="LVGL-Essential">
              <description>Add the support for RT-Thread APIs</description>
              <files>
                <file category="sourceC"      name="src/osal/lv_rtthread.c"/>
              </files>

              <RTE_Components_h>

/*   Select an operating system to use. Possible options:
 * - LV_OS_NONE
 * - LV_OS_PTHREAD
 * - LV_OS_FREERTOS
 * - LV_OS_CMSIS_RTOS2
 * - LV_OS_RTTHREAD
 * - LV_OS_WINDOWS
 * - LV_OS_CUSTOM
 */
#define LV_USE_OS   LV_OS_RTTHREAD

              </RTE_Components_h>

            </component>

            <component Cgroup="OS Abstraction Layer" Cvariant="Windows" condition="LVGL-Essential">
              <description>Add the support for Windows APIs</description>
              <files>
                <file category="sourceC"      name="src/osal/lv_windows.c"/>
              </files>

              <RTE_Components_h>

/*   Select an operating system to use. Possible options:
 * - LV_OS_NONE
 * - LV_OS_PTHREAD
 * - LV_OS_FREERTOS
 * - LV_OS_CMSIS_RTOS2
 * - LV_OS_RTTHREAD
 * - LV_OS_WINDOWS
 * - LV_OS_CUSTOM
 */
#define LV_USE_OS   LV_OS_WINDOWS

              </RTE_Components_h>

            </component>

            <component Cgroup="OS Abstraction Layer" Cvariant="User Custom" condition="LVGL-Essential">
              <description>Add a user customized (RT)OS support </description>
              <files>
                <file category="sourceC"        name="src/osal/lv_os_custom.c"    attr="config" version="1.0.0"/>
                <file category="header"         name="src/osal/lv_os_custom.h"     attr="config" version="1.0.0"/>
              </files>

              <RTE_Components_h>

/*   Select an operating system to use. Possible options:
 * - LV_OS_NONE
 * - LV_OS_PTHREAD
 * - LV_OS_FREERTOS
 * - LV_OS_CMSIS_RTOS2
 * - LV_OS_RTTHREAD
 * - LV_OS_WINDOWS
 * - LV_OS_CUSTOM
 */
#define LV_USE_OS               LV_OS_CUSTOM
#define LV_OS_CUSTOM_INCLUDE    "lv_os_custom.h"

              </RTE_Components_h>

            </component>

            <component Cgroup="Porting"  Csub="Display" Cvariant="Generic" isDefaultVariant="true" condition="LVGL-Essential">
              <description>Porting Templates for display devices.</description>
              <files>
                <file category="header"     name="examples/porting/lv_port_disp_template.h" attr="config" version="2.1.0" />
                <file category="sourceC"    name="examples/porting/lv_port_disp_template.c" attr="config" version="2.1.0" />
              </files>
            </component>
            
            <component Cgroup="Porting"  Csub="Display" Cvariant="STM32" condition="LVGL-Essential">
              <description>Porting Templates for STM32 devices.</description>
              <files>
                <file category="header"     name="examples/porting/lv_port_lcd_stm32_template.h" attr="config" version="1.0.0" />
                <file category="sourceC"    name="examples/porting/lv_port_lcd_stm32_template.c" attr="config" version="1.0.0" />
              </files>
            </component>
            
            <component Cgroup="Porting"  Csub="Input" Cvariant="Generic" isDefaultVariant="true" condition="LVGL-Essential">
              <description>Porting Templates for input devices.</description>
              <files>
                <file category="header"     name="examples/porting/lv_port_indev_template.h" attr="config" version="2.1.0" />
                <file category="sourceC"    name="examples/porting/lv_port_indev_template.c" attr="config" version="2.1.0" />
              </files>
            </component>
            
            <component Cgroup="Porting"  Csub="File System" Cvariant="Generic" isDefaultVariant="true" condition="LVGL-Essential">
              <description>Porting Templates for the file system.</description>
              <files>
                <file category="header"     name="examples/porting/lv_port_fs_template.h" attr="config" version="2.1.0" />
                <file category="sourceC"    name="examples/porting/lv_port_fs_template.c" attr="config" version="2.1.0" />
              </files>
            </component>

            <component Cgroup="Acceleration" Csub="GPU Renesas-Dave2D"  condition="LVGL-GPU-Renesas-Dave2D">
              <description>An hardware acceleration from Renesas Dave2D</description>
              <files>
                <file category="sourceC"      name="src/draw/renesas/dave2d/lv_draw_dave2d.c" />
                <file category="sourceC"      name="src/draw/renesas/dave2d/lv_draw_dave2d_arc.c" />
                <file category="sourceC"      name="src/draw/renesas/dave2d/lv_draw_dave2d_border.c" />
                <file category="sourceC"      name="src/draw/renesas/dave2d/lv_draw_dave2d_fill.c" />
                <file category="sourceC"      name="src/draw/renesas/dave2d/lv_draw_dave2d_image.c" />
                <file category="sourceC"      name="src/draw/renesas/dave2d/lv_draw_dave2d_label.c" />
                <file category="sourceC"      name="src/draw/renesas/dave2d/lv_draw_dave2d_line.c" />
                <file category="sourceC"      name="src/draw/renesas/dave2d/lv_draw_dave2d_mask_rectangle.c" />
                <file category="sourceC"      name="src/draw/renesas/dave2d/lv_draw_dave2d_triangle.c" />
                <file category="sourceC"      name="src/draw/renesas/dave2d/lv_draw_dave2d_utils.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable Renesas Dave2D */
#define LV_USE_DRAW_DAVE2D  1
              </RTE_Components_h>

            </component>

            <component Cgroup="Acceleration" Csub="GPU NXP-PXP"  condition="LVGL-GPU-NXP-PXP">
              <description>An hardware acceleration from NXP-PXP</description>
              <files>
                <file category="sourceC"      name="src/draw/nxp/pxp/lv_draw_buf_pxp.c" />
                <file category="sourceC"      name="src/draw/nxp/pxp/lv_draw_pxp.c" />
                <file category="sourceC"      name="src/draw/nxp/pxp/lv_draw_pxp_fill.c" />
                <file category="sourceC"      name="src/draw/nxp/pxp/lv_draw_pxp_img.c" />
                <file category="sourceC"      name="src/draw/nxp/pxp/lv_draw_pxp_layer.c" />
                <file category="sourceC"      name="src/draw/nxp/pxp/lv_pxp_cfg.c" />
                <file category="sourceC"      name="src/draw/nxp/pxp/lv_pxp_osa.c" />
                <file category="sourceC"      name="src/draw/nxp/pxp/lv_pxp_utils.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable NXP PXP */
#define LV_USE_DRAW_PXP     1
              </RTE_Components_h>

            </component>

            <component Cgroup="Acceleration" Csub="GPU NXP-VGLite"  condition="LVGL-GPU-NXP-VGLite">
              <description>An hardware acceleration from NXP-VGLite</description>
              <files>
                <file category="sourceC"      name="src/draw/nxp/vglite/lv_draw_buf_vglite.c" />
                <file category="sourceC"      name="src/draw/nxp/vglite/lv_draw_vglite.c" />
                <file category="sourceC"      name="src/draw/nxp/vglite/lv_draw_vglite_arc.c" />
                <file category="sourceC"      name="src/draw/nxp/vglite/lv_draw_vglite_border.c" />
                <file category="sourceC"      name="src/draw/nxp/vglite/lv_draw_vglite_fill.c" />
                <file category="sourceC"      name="src/draw/nxp/vglite/lv_draw_vglite_img.c" />
                <file category="sourceC"      name="src/draw/nxp/vglite/lv_draw_vglite_label.c" />
                <file category="sourceC"      name="src/draw/nxp/vglite/lv_draw_vglite_layer.c" />
                <file category="sourceC"      name="src/draw/nxp/vglite/lv_draw_vglite_line.c" />
                <file category="sourceC"      name="src/draw/nxp/vglite/lv_draw_vglite_triangle.c" />
                <file category="sourceC"      name="src/draw/nxp/vglite/lv_vglite_buf.c" />
                <file category="sourceC"      name="src/draw/nxp/vglite/lv_vglite_matrix.c" />
                <file category="sourceC"      name="src/draw/nxp/vglite/lv_vglite_path.c" />
                <file category="sourceC"      name="src/draw/nxp/vglite/lv_vglite_utils.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable NXP VGLite */
#define LV_USE_DRAW_VGLITE          1
              </RTE_Components_h>

            </component>

            <component Cgroup="Acceleration" Csub="GPU VG-Lite">
              <description>An hardware acceleration from VG-Lite GPU</description>
              <files>
                <file category="sourceC"      name="src/draw/vg_lite/lv_draw_buf_vg_lite.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_draw_vg_lite.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_draw_vg_lite_arc.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_draw_vg_lite_border.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_draw_vg_lite_box_shadow.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_draw_vg_lite_fill.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_draw_vg_lite_img.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_draw_vg_lite_label.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_draw_vg_lite_layer.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_draw_vg_lite_line.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_draw_vg_lite_mask_rect.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_draw_vg_lite_triangle.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_draw_vg_lite_vector.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_vg_lite_decoder.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_vg_lite_math.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_vg_lite_path.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_vg_lite_grad.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_vg_lite_pending.c" />
                <file category="sourceC"      name="src/draw/vg_lite/lv_vg_lite_utils.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable VG-Lite GPU */
#define LV_USE_DRAW_VG_LITE          1
              </RTE_Components_h>

            </component>

            <component Cgroup="Acceleration" Csub="GPU SDL2" Cversion="2.0.0">
              <description>Using existing SDL2 APIs for acceleration</description>
              <files>
                <file category="sourceC"      name="src/draw/sdl/lv_draw_sdl.c"/>
              </files>

              <RTE_Components_h>

/*! \brief enable SDL2 acceleration*/
#define LV_USE_DRAW_SDL 1

              </RTE_Components_h>

            </component>

            <!-- not available yet 
            <component Cgroup="Acceleration" Csub="GPU Arm-2D"  isDefaultVariant="true" Cversion="2.0.0">
              <description>A 2D image processing library from Arm (i.e. Arm-2D) for All Cortex-M processors including Cortex-M0</description>
              <files>
                <file category="sourceC"      name="src/draw/arm2d/lv_gpu_arm2d.c" condition="Arm-2D"/>
              </files>

              <RTE_Components_h>

/*! \brief enable Arm-2D support*/
#define LV_USE_GPU_ARM2D 1

              </RTE_Components_h>

            </component>

            <component Cgroup="Acceleration" Csub="GPU STM32-DMA2D"  condition="LVGL-GPU-STM32-DMA2D">
              <description>An hardware acceleration from STM32-DMA2D</description>
              <files>
                <file category="sourceC"      name="src/draw/stm32_dma2d/lv_gpu_stm32_dma2d.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable STM32 DMA2D */
#define LV_USE_GPU_STM32_DMA2D      1
              </RTE_Components_h>

            </component>

            <component Cgroup="Acceleration" Csub="GPU SWM341-DMA2D"  condition="LVGL-GPU-SWM341-DMA2D">
              <description>An hardware acceleration from SWM341-DMA2D</description>
              <files>
                <file category="sourceC"      name="src/draw/swm341_dma2d/lv_gpu_swm341_dma2d.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable SWM341 DMA2D */
#define LV_USE_GPU_SWM341_DMA2D      1
              </RTE_Components_h>

            </component>

            <component Cgroup="Acceleration" Csub="GPU GD32-IPA"  condition="LVGL-GPU-GD32-IPA">
              <description>An hardware acceleration from GD32-IPA</description>
              <files>
                <file category="sourceC"      name="src/draw/gd32_ipa/lv_gpu_gd32_ipa.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable GD32 IPA */
#define LV_USE_GPU_GD32_IPA      1
              </RTE_Components_h>

            </component>
            -->
            
            <!--
            <component Cgroup="LVGL9" Csub="PikaScript Binding" Cversion="0.2.0" condition="LVGL-Essential">
              <description>Add the binding for PikaScript, an ultra-light-weight python VM.</description>
              <files>
                <file category="doc"        name="pikascript/README.md"/>
                <file category="sourceC"    name="pikascript/pika_lvgl.c" />
                <file category="sourceC"    name="pikascript/pika_lvgl_indev_t.c" />
                <file category="sourceC"    name="pikascript/pika_lvgl_lv_event.c" />
                <file category="sourceC"    name="pikascript/pika_lvgl_lv_obj.c" />
                <file category="sourceC"    name="pikascript/pika_lvgl_lv_style_t.c" />
                <file category="sourceC"    name="pikascript/pika_lv_point_t.c" />
                <file category="sourceC"    name="pikascript/pika_lv_timer_t.c" />
                <file category="sourceC"    name="pikascript/pika_lv_wegit.c" />
              </files>
              <Pre_Include_Local_Component_h>

/* enabling PikaScript Binding */
#define PIKASCRIPT     1
              </Pre_Include_Local_Component_h>
              <RTE_Components_h>

/*! \brief enable PikaScript Binding */
#define LV_USE_PIKASCRIPT_BINDING          1
              </RTE_Components_h>

            </component>
            -->

            <component Cgroup="Libraries and Others" Csub="Libs Barcode"  condition="LVGL-Essential">
              <description>Add the Barcode code library</description>
              <files>
                <!-- src/libs/barcode -->
                <file category="sourceC"    name="src/libs/barcode/lv_barcode.c" />
                <file category="sourceC"    name="src/libs/barcode/code128.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable Barcode support */
#define LV_USE_BARCODE          1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Libs BMP"  condition="LVGL-Essential">
              <description>Add BMP decoder library</description>
              <files>
                <!-- src/libs/bmp -->
                <file category="sourceC"    name="src/libs/bmp/lv_bmp.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable BMP support */
#define LV_USE_BMP          1
              </RTE_Components_h>

            </component>


            <component Cgroup="Libraries and Others" Csub="Libs ffmpeg"  condition="LVGL-Essential">
              <description>Add FFmpeg library for image decoding and playing videos, an extra librbary is required.</description>
              <files>
                <!-- src/libs/ffmpeg -->
                <file category="sourceC"    name="src/libs/ffmpeg/lv_ffmpeg.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable ffmpeg support */
#define LV_USE_FFMPEG         1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Libs freetype"  condition="LVGL-Essential">
              <description>Add FreeType library, an extra librbary is required.</description>
              <files>
                <!-- src/libs/freetype -->
                <file category="sourceC"    name="src/libs/freetype/lv_freetype.c" />
                <file category="sourceC"    name="src/libs/freetype/lv_freetype_image.c" />
                <file category="sourceC"    name="src/libs/freetype/lv_freetype_outline.c" />
                <file category="sourceC"    name="src/libs/freetype/lv_ftsystem.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable freetype support */
#define LV_USE_FREETYPE          1
              </RTE_Components_h>

            </component>


            <component Cgroup="Libraries and Others" Csub="Lib Filesystem" Cvariant="FATFS" condition="LVGL-Essential">
              <description>Add API for FATFS (needs to be added separately). Uses f_open, f_read, etc</description>
              <files>
                <!-- src/libs/fsdrv -->
                <file category="sourceC"    name="src/libs/fsdrv/lv_fs_fatfs.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable FATFS support */
#define LV_USE_FS_FATFS         1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Lib Filesystem" Cvariant="Memory-Mapped Files" isDefaultVariant="true" condition="LVGL-Essential">
              <description>Add API for memory-mapped file access.</description>
              <files>
                <!-- src/libs/fsdrv -->
                <file category="sourceC"    name="src/libs/fsdrv/lv_fs_memfs.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable memory-mapped file access */
#define LV_USE_FS_MEMFS         1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Lib Filesystem" Cvariant="POSIX" condition="LVGL-Essential">
              <description>Add API for file access via POSIX</description>
              <files>
                <!-- src/libs/fsdrv -->
                <file category="sourceC"    name="src/libs/fsdrv/lv_fs_posix.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable POSIX file access */
#define LV_USE_FS_POSIX         1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Lib Filesystem" Cvariant="STDIO" condition="LVGL-Essential">
              <description>Add API for file access via STDIO</description>
              <files>
                <!-- src/libs/fsdrv -->
                <file category="sourceC"    name="src/libs/fsdrv/lv_fs_stdio.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable STDIO file access */
#define LV_USE_FS_STDIO         1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Lib Filesystem" Cvariant="LittleFs" condition="LVGL-Essential">
              <description>Add API for file access via LittleFs</description>
              <files>
                <!-- src/libs/fsdrv -->
                <file category="sourceC"    name="src/libs/fsdrv/lv_fs_littlefs.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable LittleFs file access */
#define LV_USE_FS_LITTLEFS      1
              </RTE_Components_h>

            </component>

            <!--
            <component Cgroup="Libraries and Others" Csub="Lib Filesystem" Cvariant="WIN32" condition="LVGL-Essential">
              <description>Add API for file access via STDIO</description>
              <files>
                <file category="sourceC"    name="src/libs/fsdrv/lv_fs_cbfs.c" />
                <file category="sourceC"    name="src/libs/fsdrv/lv_fs_win32.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable WIN32 file access */
#define LV_USE_FS_WIN32         1
              </RTE_Components_h>

            </component>
            -->

            <component Cgroup="Libraries and Others" Csub="Libs GIF"  condition="LVGL-Essential">
              <description>Add GIF support</description>
              <files>
                <!-- src/libs/gif -->
                <file category="sourceC"    name="src/libs/gif/lv_gif.c" />
                <file category="sourceC"    name="src/libs/gif/gifdec.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable gif support */
#define LV_USE_GIF         1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Libs libjpeg-turbo decoder"  condition="LVGL-Essential">
              <description>Add libjpeg-turbo decoder library</description>
              <files>
                <!-- src/libs/libjpeg_turbo -->
                <file category="sourceC"    name="src/libs/libjpeg_turbo/lv_libjpeg_turbo.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable ibjpeg-turbo decoder */
#define LV_USE_LIBJPEG_TURBO         1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Libs PNG"  condition="LVGL-Essential">
              <description>Add PNG decoder(libpng) library</description>
              <files>
                <!-- src/libs/png -->
                <file category="sourceC"    name="src/libs/libpng/lv_libpng.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable PNG decoder(libpng) library */
#define LV_USE_LIBPNG          1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Libs LODEPNG"  condition="LVGL-Essential">
              <description>Add LODEPNG decoder library</description>
              <files>
                <!-- src/libs/lodepng -->
                <file category="sourceC"    name="src/libs/lodepng/lodepng.c" />
                <file category="sourceC"    name="src/libs/lodepng/lv_lodepng.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable LODEPNG decoder library */
#define LV_USE_LODEPNG          1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Libs LZ4"  condition="LVGL-Essential">
              <description>Add LZ4 compress/decompress lib</description>
              <files>
                <!-- src/libs/lz4 -->
                <file category="sourceC"    name="src/libs/lz4/lz4.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable LZ4 compress/decompress lib */
#define LV_USE_LZ4              1
#define LV_USE_LZ4_INTERNAL     1
#define LV_USE_LZ4_EXTERNAL     0
              </RTE_Components_h>

            </component>
            
            <component Cgroup="Libraries and Others" Csub="Libs QRCode"  condition="LVGL-Essential">
              <description>Add QR code library</description>
              <files>
                <!-- src/libs/qrcode -->
                <file category="sourceC"    name="src/libs/qrcode/lv_qrcode.c" />
                <file category="sourceC"    name="src/libs/qrcode/qrcodegen.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable QR code library */
#define LV_USE_QRCODE         1
              </RTE_Components_h>

            </component>


            <component Cgroup="Libraries and Others" Csub="Libs RLE"  condition="LVGL-Essential">
              <description>Add LVGL's version of RLE compression method support</description>
              <files>
                <!-- src/libs/rle -->
                <file category="sourceC"    name="src/libs/rle/lv_rle.c" />
              </files>

              <RTE_Components_h>
/*! \brief LVGL's version of RLE compression method support */
#define LV_USE_RLE          1
              </RTE_Components_h>
            </component>

            <component Cgroup="Libraries and Others" Csub="Libs RLOTTIE"  condition="LVGL-Essential">
              <description>Add Rlottie library, an extra librbary is required.</description>
              <files>
                <!-- src/libs/rlottie -->
                <file category="sourceC"    name="src/libs/rlottie/lv_rlottie.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable Rlottie library */
#define LV_USE_RLOTTIE         1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Libs ThorVG"  condition="LVGL-Essential">
              <description>Add ThorVG (vector graphics library), an extra librbary is required.</description>
              <files>
                <!-- src/libs/thorvg -->
                <file category="sourceCpp"    name="src/libs/thorvg/tvgAnimation.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgBezier.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgCanvas.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgCapi.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgCompressor.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgFill.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgInitializer.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgLoader.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgMath.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgPaint.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgPicture.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgRawLoader.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgRender.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSaver.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgScene.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgShape.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgStr.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSvgCssStyle.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSvgLoader.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSvgPath.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSvgSceneBuilder.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSvgUtil.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSwCanvas.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSwFill.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSwImage.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSwMath.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSwMemPool.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSwRaster.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSwRenderer.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSwRle.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSwShape.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgSwStroke.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgTaskScheduler.cpp" />
                <file category="sourceCpp"    name="src/libs/thorvg/tvgXmlParser.cpp" />
              </files>

              <RTE_Components_h>

/*! \brief enable ThorVG (vector graphics library) */
#define LV_USE_VECTOR_GRAPHIC       1
#define LV_USE_THORVG_INTERNAL      1
#define LV_USE_THORVG_EXTERNAL      0
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Tiny TTF"  condition="LVGL-Essential">
              <description>Add Built-in TTF decoder</description>
              <files>
                <!-- src/libs/tiny_ttf -->
                <file category="sourceC"    name="src/libs/tiny_ttf/lv_tiny_ttf.c" />
                
              </files>

              <RTE_Components_h>

/*! \brief enable Built-in TTF decoder */
#define LV_USE_TINY_TTF         1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Libs sJPG"  condition="LVGL-Essential">
              <description>Add JPG + split JPG decoder library. Split JPG is a custom format optimized for embedded systems.</description>
              <files>
                <!-- src/libs/tjpgd -->
                <file category="sourceC"    name="src/libs/tjpgd/lv_tjpgd.c" />
                <file category="sourceC"    name="src/libs/tjpgd/tjpgd.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable JPG + split JPG decoder library. */
#define LV_USE_TJPGD         1
              </RTE_Components_h>

            </component>


            <component Cgroup="Libraries and Others" Csub="File Explorer"  condition="LVGL-Essential">
              <description>Add a file explorer</description>
              <files>
                <!-- src/others/file_explorer -->
                <file category="sourceC"    name="src/others/file_explorer/lv_file_explorer.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable file explorer support */
#define LV_USE_FILE_EXPLORER         1
              </RTE_Components_h>

            </component>
            
            <component Cgroup="Libraries and Others" Csub="Fragment"  condition="LVGL-Essential">
              <description>Add the Fragment service</description>
              <files>
                <!-- src/others/fragment -->
                <file category="sourceC"            name="src/others/fragment/lv_fragment.c" />
                <file category="sourceC"            name="src/others/fragment/lv_fragment_manager.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable lv_obj fragment */
#define LV_USE_FRAGMENT         1
              </RTE_Components_h>

            </component>
            
            <component Cgroup="Libraries and Others" Csub="Grid Navigation"  condition="LVGL-Essential">
              <description>Add the Grid Navigation service</description>
              <files>
                <!-- src/others/gridnav -->
                <file category="sourceC"            name="src/others/gridnav/lv_gridnav.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable the Grid Navigation support*/
#define LV_USE_GRIDNAV          1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="IME Pinyin"  condition="LVGL-Essential">
              <description>Add Pinyin input method</description>
              <files>
                <!-- src/others/ime -->
                <file category="sourceC"            name="src/others/ime/lv_ime_pinyin.c" />
              </files>

              <RTE_Components_h>

/*! \brief Pinyin input method */
#define LV_USE_IME_PINYIN       1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Image Font"  condition="LVGL-Essential">
              <description>Add Support for using images as font in label or span widgets</description>
              <files>
                <!-- src/others/imgfont -->
                <file category="sourceC"            name="src/others/imgfont/lv_imgfont.c" />
              </files>

              <RTE_Components_h>

/*! \brief Support using images as font in label or span widgets */
#define LV_USE_IMGFONT          1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Monkey"  condition="LVGL-Essential">
              <description>Add the Monkey test service</description>
              <files>
                <!-- src/others/monkey -->
                <file category="sourceC"            name="src/others/monkey/lv_monkey.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable Monkey test*/
#define LV_USE_MONKEY           1
              </RTE_Components_h>

            </component>

            <component Cgroup="Libraries and Others" Csub="Snapshot"  condition="LVGL-Essential">
              <description>Add the API to take snapshot for object</description>
              <files>
                <!-- src/others/snapshot -->
                <file category="sourceC"            name="src/others/snapshot/lv_snapshot.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable API to take snapshot for object */
#define LV_USE_SNAPSHOT         1
              </RTE_Components_h>

            </component>

            <component Cgroup="Demos" Csub="Assets"  condition="LVGL-Essential">
              <description>Add the official benchmark.</description>
              <files>
                <!-- demos/benchmark -->
                <file category="sourceC"    name="demos/benchmark/assets/img_benchmark_cogwheel_alpha256.c" />
                <file category="sourceC"    name="demos/benchmark/assets/img_benchmark_cogwheel_argb.c" />
                <file category="sourceC"    name="demos/benchmark/assets/img_benchmark_cogwheel_indexed16.c" />
                <file category="sourceC"    name="demos/benchmark/assets/img_benchmark_cogwheel_rgb.c" />

                <file category="sourceC"    name="demos/benchmark/assets/lv_font_benchmark_montserrat_12_compr_az.c.c" />
                <file category="sourceC"    name="demos/benchmark/assets/lv_font_benchmark_montserrat_16_compr_az.c.c" />
                <file category="sourceC"    name="demos/benchmark/assets/lv_font_benchmark_montserrat_28_compr_az.c.c" />
                
                <!-- demos/multilang -->
                <file category="sourceC"    name="demos/multilang/assets/img_multilang_like.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_1.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_2.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_3.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_4.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_5.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_6.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_7.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_8.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_9.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_10.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_11.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_12.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_13.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_14.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_15.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_16.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_17.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_18.c" />
                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_19.c" />


                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_22.c" />

                <file category="sourceC"    name="demos/multilang/assets/avatars/img_multilang_avatar_25.c" />
                
                <file category="sourceC"    name="demos/multilang/assets/emojis/img_emoji_artist_palette.c" />
                <file category="sourceC"    name="demos/multilang/assets/emojis/img_emoji_books.c" />
                <file category="sourceC"    name="demos/multilang/assets/emojis/img_emoji_camera_with_flash.c" />
                <file category="sourceC"    name="demos/multilang/assets/emojis/img_emoji_cat_face.c" />
                <file category="sourceC"    name="demos/multilang/assets/emojis/img_emoji_deciduous_tree.c" />
                <file category="sourceC"    name="demos/multilang/assets/emojis/img_emoji_dog_face.c" />
                <file category="sourceC"    name="demos/multilang/assets/emojis/img_emoji_earth_globe_europe_africa.c" />
                <file category="sourceC"    name="demos/multilang/assets/emojis/img_emoji_flexed_biceps.c" />
                <file category="sourceC"    name="demos/multilang/assets/emojis/img_emoji_movie_camera.c" />
                <file category="sourceC"    name="demos/multilang/assets/emojis/img_emoji_red_heart.c" />
                <file category="sourceC"    name="demos/multilang/assets/emojis/img_emoji_rocket.c" />
                <file category="sourceC"    name="demos/multilang/assets/emojis/img_emoji_soccer_ball.c" />
                
                <file category="sourceC"    name="demos/multilang/assets/fonts/font_multilang_large.c" />
                <file category="sourceC"    name="demos/multilang/assets/fonts/font_multilang_small.c" />
                
                <!-- demos/music -->
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_wave_top_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_wave_top.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_wave_bottom_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_wave_bottom.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_slider_knob_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_slider_knob.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_logo.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_list_border_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_list_border.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_icon_4_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_icon_4.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_icon_3_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_icon_3.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_icon_2_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_icon_2.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_icon_1_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_icon_1.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_cover_3_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_cover_3.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_cover_2_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_cover_2.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_cover_1_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_cover_1.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_corner_right_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_corner_right.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_corner_left_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_corner_left.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_rnd_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_rnd.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_prev_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_prev.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_play_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_play.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_pause_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_pause.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_next_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_next.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_loop_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_loop.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_list_play_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_list_play.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_list_pause_large.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_list_pause.c" />
                <file category="sourceC"    name="demos/music/assets/img_lv_demo_music_btn_corner_large.c" />

                <!-- demos/widgets -->
                <file category="sourceC"    name="demos/widgets/assets/img_clothes.c" />
                <file category="sourceC"    name="demos/widgets/assets/img_demo_widgets_avatar.c" />
                <file category="sourceC"    name="demos/widgets/assets/img_demo_widgets_needle.c" />
                <file category="sourceC"    name="demos/widgets/assets/img_lvgl_logo.c" />

                <!-- demos/transform -->
                <file category="sourceC"    name="demos/transform/assets/img_transform_avatar_15.c" />
                
                <!-- demos/vector_graphic -->
                <file category="sourceC"    name="demos/vector_graphic/assets/img_demo_vector_avatar.c" />
                
                <!-- demos/render -->
                <file category="sourceC"    name="demos/render/assets/img_render_arc_bg.c" />
                <file category="sourceC"    name="demos/render/assets/img_render_lvgl_logo_argb8888.c" />
                <file category="sourceC"    name="demos/render/assets/img_render_lvgl_logo_rgb565.c" />
                <file category="sourceC"    name="demos/render/assets/img_render_lvgl_logo_rgb888.c" />
                <file category="sourceC"    name="demos/render/assets/img_render_lvgl_logo_xrgb8888.c" />
              </files>

            </component>

            <component Cgroup="Demos" Csub="Benchmark"  condition="Demo-Benchmark">
              <description>Add the official benchmark.</description>
              <files>
                <!-- demos/benchmark -->
                <file category="sourceC"    name="demos/benchmark/lv_demo_benchmark.c" />
                <file category="doc"        name="demos/README.md" />
              </files>

              <RTE_Components_h>

/*! \brief enable demo:bencharmk */
#define LV_USE_DEMO_BENCHMARK           1
              </RTE_Components_h>

            </component>

            <component Cgroup="Demos" Csub="Flex Layout" condition="LVGL-Essential-Assets">
              <description>Add the Flex layout demo</description>
              <files>
                <!-- demos/flex_layout -->
                <file category="sourceC"    name="demos/flex_layout/lv_demo_flex_layout_ctrl_pad.c" />
                <file category="sourceC"    name="demos/flex_layout/lv_demo_flex_layout_flex_loader.c" />
                <file category="sourceC"    name="demos/flex_layout/lv_demo_flex_layout_main.c" />
                <file category="sourceC"    name="demos/flex_layout/lv_demo_flex_layout_view.c" />
                <file category="sourceC"    name="demos/flex_layout/lv_demo_flex_layout_view_child_node.c" />
                <file category="sourceC"    name="demos/flex_layout/lv_demo_flex_layout_view_ctrl_pad.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable the Flex layout demo */
#define LV_USE_DEMO_FLEX_LAYOUT         1
              </RTE_Components_h>

            </component>
 
             <component Cgroup="Demos" Csub="Keypad Encoder" condition="LVGL-Essential-Assets">
              <description>Add the demonstrate the usage of encoder and keyboard</description>
              <files>
                <!-- demos/keypad_encoder -->
                <file category="sourceC"    name="demos/keypad_encoder/lv_demo_keypad_encoder.c" />
                <file category="doc"        name="demos/keypad_encoder/README.md" />
              </files>

              <RTE_Components_h>

/*! \brief enable the demonstrate the usage of encoder and keyboard */
#define LV_USE_DEMO_KEYPAD_AND_ENCODER  1
              </RTE_Components_h>

            </component>
 
 
              <component Cgroup="Demos" Csub="Multi-Language" condition="LVGL-Essential-Assets">
              <description>Add the Smart-phone like multi-language demo.</description>
              <files>
                <!-- demos/multilang -->
                <file category="sourceC"    name="demos/multilang/lv_demo_multilang.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable the Smart-phone like multi-language demo */
#define LV_USE_DEMO_MULTILANG           1
              </RTE_Components_h>

            </component>
 
              <component Cgroup="Demos" Csub="Music Player" condition="LVGL-Essential-Assets">
              <description>Add the music player demo</description>
              <files>
                <!-- demos/music -->
                <file category="sourceC"    name="demos/music/lv_demo_music.c" />
                <file category="sourceC"    name="demos/music/lv_demo_music_list.c" />
                <file category="sourceC"    name="demos/music/lv_demo_music_main.c" />
                <file category="doc"        name="demos/README.md" />
              </files>

              <RTE_Components_h>

/*! \brief enable the music player demo */
#define LV_USE_DEMO_MUSIC               1
              </RTE_Components_h>

            </component>
 
             <component Cgroup="Demos" Csub="Scroll" condition="LVGL-Essential-Assets">
              <description>Add the demonstration for scroll settings</description>
              <files>
                <!-- demos/scroll -->
                <file category="sourceC"    name="demos/scroll/lv_demo_scroll.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable the demonstration for scroll settings */
#define LV_USE_DEMO_SCROLL              1
              </RTE_Components_h>

            </component>

             <component Cgroup="Demos" Csub="Stress Test" condition="LVGL-Essential-Assets">
              <description>Add the Stress test for LVGL</description>
              <files>
                <!-- demos/stress -->
                <file category="sourceC"    name="demos/stress/lv_demo_stress.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable the Stress test for LVGL */
#define LV_USE_DEMO_STRESS              1
              </RTE_Components_h>

            </component>
            
             <component Cgroup="Demos" Csub="Widget Transform" condition="LVGL-Essential-Assets">
              <description>Add the Widget transformation demo</description>
              <files>
                <!-- demos/transform -->
                <file category="sourceC"    name="demos/transform/lv_demo_transform.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable the Widget transformation demo */
#define LV_USE_DEMO_TRANSFORM           1
              </RTE_Components_h>

            </component>
 
              <component Cgroup="Demos" Csub="Vector Graphic" condition="LVGL-Essential-Assets">
              <description>Add the Vector graphic demo</description>
              <files>
                <!-- demos/vector_graphic -->
                <file category="sourceC"    name="demos/vector_graphic/lv_demo_vector_graphic.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable the Vector graphic demo */
#define LV_USE_DEMO_VECTOR_GRAPHIC      1
              </RTE_Components_h>

            </component>

            <component Cgroup="Demos" Csub="Widgets" condition="LVGL-Essential-Assets">
              <description>Add the demo:widgets</description>
              <files>
                <!-- demos/widgets -->
                <file category="sourceC"    name="demos/widgets/lv_demo_widgets.c" />
              </files>

              <RTE_Components_h>

/*! \brief enable demo:widgets support */
#define LV_USE_DEMO_WIDGETS             1
              </RTE_Components_h>

            </component>
            
            <component Cgroup="Demos" Csub="Render Test" condition="LVGL-Essential-Assets">
              <description>Add the Render test for each primitives. Requires at least 480x272 display</description>
              <files>
                <!-- demos/render -->
                <file category="sourceC"    name="demos/render/lv_demo_render.c" />
                <file category="doc"        name="demos/README.md" />
              </files>

              <RTE_Components_h>

/*! \brief add Render test for each primitives */
#define LV_USE_DEMO_RENDER              1
              </RTE_Components_h>

            </component>
        </bundle>
    </components>

  <!-- optional taxonomy section for defining new component Class and Group names -->
  <!--
  <taxonomy>
  </taxonomy>
  -->

</package>
